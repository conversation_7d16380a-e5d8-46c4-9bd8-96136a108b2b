import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import i18n from '@/lib/i18n';

export type Language = 'en' | 'ar';

interface LanguageState {
  language: Language;
  isRTL: boolean;
  setLanguage: (language: Language) => void;
  toggleLanguage: () => void;
  refreshApiData: () => void;
}

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set, get) => ({
      language: 'en',
      isRTL: false,

      setLanguage: (language: Language) => {
        const isRTL = language === 'ar';

        // Update i18n language
        i18n.changeLanguage(language);

        // Update document direction, lang attribute, and font class
        if (typeof document !== 'undefined') {
          document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
          document.documentElement.lang = language;

          // Remove existing font classes
          document.documentElement.classList.remove('font-en', 'font-ar');

          // Add appropriate font class based on language
          document.documentElement.classList.add(`font-${language}`);
        }

        set({ language, isRTL });

        // Refresh API data when language changes
        get().refreshApiData();
      },

      toggleLanguage: () => {
        const currentLanguage = get().language;
        const newLanguage: Language = currentLanguage === 'en' ? 'ar' : 'en';
        get().setLanguage(newLanguage);
      },

      refreshApiData: () => {
        // Import stores dynamically to avoid circular dependencies
        import('./systemSettingsStore').then(({ useSystemSettingsStore }) => {
          useSystemSettingsStore.getState().refreshSystemSettings();
        });

        import('./footerStore').then(({ useFooterStore }) => {
          useFooterStore.getState().refreshFooter();
        });

        import('./servicesStore').then(({ useServicesStore }) => {
          useServicesStore.getState().refreshServices();
        });

        import('./categoriesStore').then(({ useCategoriesStore }) => {
          useCategoriesStore.getState().refreshCategories();
        });

        import('./liveMarketInsightsStore').then(({ useLiveMarketInsightsStore }) => {
          useLiveMarketInsightsStore.getState().refreshLiveMarketInsights();
        });

        import('./blogStore').then(({ useBlogStore }) => {
          useBlogStore.getState().refreshBlogs();
        });
      },
    }),
    {
      name: 'language-storage',
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Ensure i18n is updated when store is rehydrated
          state.setLanguage(state.language);
        }
      },
    }
  )
);
