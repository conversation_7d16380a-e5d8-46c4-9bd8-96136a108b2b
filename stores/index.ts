// Export all stores
export * from './languageStore';
export * from './themeStore';
export * from './sidebarStore';
export * from './systemSettingsStore';
export * from './footerStore';
export * from './servicesStore';
export * from './categoriesStore';
export * from './blogStore';
export * from './liveMarketInsightsStore';

// Export API utilities
export * from '../lib/api';

// Export types
export * from '../types/api';

// Store initialization hook
import { useSystemSettingsStore } from './systemSettingsStore';
import { useFooterStore } from './footerStore';
import { useServicesStore } from './servicesStore';
import { useCategoriesStore } from './categoriesStore';
import { useBlogStore } from './blogStore';
import { useLiveMarketInsightsStore } from './liveMarketInsightsStore';

/**
 * Hook to initialize all API stores
 * Call this in your main layout or app component to ensure data is loaded
 */
// export const useInitializeStores = () => {
//   const initSystemSettings = useSystemSettingsStore((state) => state.fetchSystemSettings);
//   const initBannerSettings = useBannerStore((state) => state.fetchBannerSettings);
//   const initFooter = useFooterStore((state) => state.fetchFooter);
//   const initServices = useServicesStore((state) => state.fetchServices);
//   const initCategories = useCategoriesStore((state) => state.fetchCategories);
//   const initBlogs = useBlogStore((state) => state.fetchBlogs);

//   const initializeAll = async () => {
//     try {
//       // Initialize all stores in parallel
//       await Promise.allSettled([
//         initSystemSettings(),
//         initBannerSettings(),
//         initFooter(),
//         initServices(),
//         initCategories(),
//         initBlogs(),
//       ]);
//     } catch (error) {
//       console.error('Error initializing stores:', error);
//     }
//   };

//   return { initializeAll };
// };

/**
 * Hook to refresh all API data
 * Useful when language changes or when you need fresh data
 */
export const useRefreshAllStores = () => {
  const refreshSystemSettings = useSystemSettingsStore((state) => state.refreshSystemSettings);
  const refreshFooter = useFooterStore((state) => state.refreshFooter);
  const refreshServices = useServicesStore((state) => state.refreshServices);
  const refreshCategories = useCategoriesStore((state) => state.refreshCategories);
  const refreshBlogs = useBlogStore((state) => state.refreshBlogs);

  const refreshAll = async () => {
    try {
      // Refresh all stores in parallel
      await Promise.allSettled([
        refreshSystemSettings(),
        refreshFooter(),
        refreshServices(),
        refreshCategories(),
        refreshBlogs(),
      ]);
    } catch (error) {
      console.error('Error refreshing stores:', error);
    }
  };

  return { refreshAll };
};

/**
 * Hook to get loading states from all stores
 */
export const useGlobalLoadingState = () => {
  const systemSettingsLoading = useSystemSettingsStore((state) => state.loading);
  const footerLoading = useFooterStore((state) => state.loading);
  const servicesLoading = useServicesStore((state) => state.loading);
  const categoriesLoading = useCategoriesStore((state) => state.loading);
  const blogLoading = useBlogStore((state) => state.loading);

  const isAnyLoading = systemSettingsLoading || footerLoading || servicesLoading || categoriesLoading || blogLoading;
  const isAllLoading = systemSettingsLoading && footerLoading && servicesLoading && categoriesLoading && blogLoading;

  return {
    isAnyLoading,
    isAllLoading,
    systemSettingsLoading,
    footerLoading,
    servicesLoading,
    categoriesLoading,
    blogLoading,
  };
};

/**
 * Hook to get error states from all stores
 */
export const useGlobalErrorState = () => {
  const systemSettingsError = useSystemSettingsStore((state) => state.error);
  const footerError = useFooterStore((state) => state.error);
  const servicesError = useServicesStore((state) => state.error);
  const categoriesError = useCategoriesStore((state) => state.error);
  const blogError = useBlogStore((state) => state.error);

  const errors = [systemSettingsError, footerError, servicesError, categoriesError, blogError].filter(Boolean);
  const hasErrors = errors.length > 0;

  return {
    hasErrors,
    errors,
    systemSettingsError,
    footerError,
    servicesError,
    categoriesError,
    blogError,
  };
};
