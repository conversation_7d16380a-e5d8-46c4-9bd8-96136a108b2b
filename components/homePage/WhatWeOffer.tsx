"use client"

import { useTranslation } from "react-i18next"
import { ServiceCard } from "@/components/common/ServiceCard"
import { Button } from "@/components/ui/button"
import { Icon } from "@/components/common/Icon"
import { useSystemSettingsWithStore, useActiveServicesWithStore } from "@/hooks"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"

export function WhatWeOffer() {
  const { t } = useTranslation()
  const router = useRouter()
  const { getSettingByKey } = useSystemSettingsWithStore()
  const { data: activeServices = [], isLoading } = useActiveServicesWithStore()

  const services = activeServices.map((service) => ({
    key: service.slug,
    title: service.name,
    description: service.description,
    image: service.image || ""
  }))

  const handleServiceClick = (serviceKey: string) => {
    console.log(`Clicked on ${serviceKey} service`)
  }

  const handleShowMoreProducts = () => {
    router.push(getSettingByKey("WHAT_WE_OFFER_CTA_URL")?.value || "/")
  }

  if (services.length === 0) return null

  return (
    <section id="services" className="py-16">
      <div className="container mx-auto">
        <h2 className="text-[18px] font-bold text-primary-500 mb-4">
          {getSettingByKey("WHAT_WE_OFFER_CAPTION")?.value ||
            t("services.services")}
        </h2>
        <div className="flex justify-between items-center">
          <div className="text-start mb-12">
            <h2 className="text-3xl md:text-6xl font-bold text-secondary-500 dark:text-white-500 mb-4">
              {getSettingByKey("WHAT_WE_OFFER_HEADING")?.value ||
                t("services.title")}
            </h2>
            <p className="text-lg text-secondary-500 dark:text-white-500 max-w-2xl">
              {getSettingByKey("WHAT_WE_OFFER_RICH_TEXT")?.value ||
                t("services.subtitle")}
            </p>
          </div>
          <div className="text-end">
            <Button
              onClick={handleShowMoreProducts}
              className="bg-button-background-primary border border-button-background-primary-disable rounded-none hover:bg-button-background-primary  text-white-50 px-5 py-3 font-medium transition-colors duration-200 flex items-center gap-2"
            >
              {getSettingByKey("WHAT_WE_OFFER_CTA_TEXT")?.value ||
                t("services.show_more_products")}
              <Icon name="arrow-right" size={16} />
            </Button>
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {services.map((service) => (
            <ServiceCard
              key={service.key}
              title={service.title}
              description={service.description}
              image={service.image}
              onClick={() => handleServiceClick(service.key)}
              className="h-full"
            />
          ))}
        </div>
      </div>
    </section>
  )
}
