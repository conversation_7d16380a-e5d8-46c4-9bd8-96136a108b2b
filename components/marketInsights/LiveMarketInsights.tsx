'use client';

import { useEffect } from 'react';
import { 
  useMarketPrices, 
  useMarketNews, 
  useMarketIndicators,
  useLiveMarketInsightsStore,
  useMarketInsightsLoading,
  useMarketInsightsError,
  useAutoRefreshState
} from '@/stores/liveMarketInsightsStore';

export default function LiveMarketInsights() {
  const prices = useMarketPrices();
  const news = useMarketNews();
  const indicators = useMarketIndicators();
  const loading = useMarketInsightsLoading();
  const error = useMarketInsightsError();
  const { autoRefresh, refreshInterval } = useAutoRefreshState();
  
  const { 
    fetchLiveMarketInsights, 
    startAutoRefresh, 
    stopAutoRefresh,
    setRefreshInterval 
  } = useLiveMarketInsightsStore();

  useEffect(() => {
    // Fetch data when component mounts
    fetchLiveMarketInsights();
  }, [fetchLiveMarketInsights]);

  const handleToggleAutoRefresh = () => {
    if (autoRefresh) {
      stopAutoRefresh();
    } else {
      startAutoRefresh();
    }
  };

  const handleRefreshIntervalChange = (newInterval: number) => {
    setRefreshInterval(newInterval);
  };

  if (loading && prices.length === 0) {
    return <div className="loading">Loading market insights...</div>;
  }

  if (error) {
    return <div className="error">Error: {error}</div>;
  }

  return (
    <div className="live-market-insights">
      <div className="header flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Live Market Insights</h1>
        
        <div className="controls flex items-center gap-4">
          <button
            onClick={handleToggleAutoRefresh}
            className={`px-4 py-2 rounded-lg ${
              autoRefresh 
                ? 'bg-red-500 hover:bg-red-600 text-white' 
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
          >
            {autoRefresh ? 'Stop Auto Refresh' : 'Start Auto Refresh'}
          </button>
          
          <select
            value={refreshInterval}
            onChange={(e) => handleRefreshIntervalChange(Number(e.target.value))}
            className="px-3 py-2 border rounded-lg"
          >
            <option value={10000}>10 seconds</option>
            <option value={30000}>30 seconds</option>
            <option value={60000}>1 minute</option>
            <option value={300000}>5 minutes</option>
          </select>
        </div>
      </div>

      {/* Market Prices */}
      <section className="market-prices mb-8">
        <h2 className="text-2xl font-semibold mb-4">Market Prices</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {prices.map((price) => (
            <div key={price.id} className="price-card bg-white border rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-semibold">{price.symbol}</h3>
                  <p className="text-sm text-gray-600">{price.name}</p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold">${price.current_price.toFixed(2)}</p>
                  <p className={`text-sm ${
                    price.price_change_percentage_24h >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {price.price_change_percentage_24h >= 0 ? '+' : ''}
                    {price.price_change_percentage_24h.toFixed(2)}%
                  </p>
                </div>
              </div>
              {price.volume_24h && (
                <p className="text-xs text-gray-500">
                  Volume: ${price.volume_24h.toLocaleString()}
                </p>
              )}
            </div>
          ))}
        </div>
      </section>

      {/* Market Indicators */}
      <section className="market-indicators mb-8">
        <h2 className="text-2xl font-semibold mb-4">Market Indicators</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {indicators.map((indicator) => (
            <div key={indicator.id} className="indicator-card bg-white border rounded-lg p-4 shadow-sm">
              <h3 className="font-semibold text-sm mb-1">{indicator.name}</h3>
              <p className="text-2xl font-bold mb-1">
                {indicator.value} {indicator.unit}
              </p>
              <p className={`text-sm ${
                indicator.change_percentage >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {indicator.change_percentage >= 0 ? '+' : ''}
                {indicator.change_percentage.toFixed(2)}%
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Market News */}
      <section className="market-news">
        <h2 className="text-2xl font-semibold mb-4">Market News</h2>
        <div className="space-y-4">
          {news.map((newsItem) => (
            <article key={newsItem.id} className="news-card bg-white border rounded-lg p-6 shadow-sm">
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold mb-2">{newsItem.title}</h3>
                  <p className="text-gray-600 mb-3">{newsItem.summary}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>{newsItem.source}</span>
                    <span>•</span>
                    <span>{new Date(newsItem.published_at).toLocaleDateString()}</span>
                    {newsItem.impact_level && (
                      <>
                        <span>•</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          newsItem.impact_level === 'high' ? 'bg-red-100 text-red-800' :
                          newsItem.impact_level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {newsItem.impact_level} impact
                        </span>
                      </>
                    )}
                  </div>
                </div>
                {newsItem.image_url && (
                  <img 
                    src={newsItem.image_url} 
                    alt={newsItem.title}
                    className="w-24 h-24 object-cover rounded-lg ml-4"
                  />
                )}
              </div>
            </article>
          ))}
        </div>
      </section>

      {loading && (
        <div className="fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg">
          Refreshing...
        </div>
      )}
    </div>
  );
}
