"use client"

import { useState, useRef } from "react"
import { useLivePrices } from "../../hooks/useLivePrices"
import { cn } from "@/lib/utils"
import { ArrowUpRight, ArrowDownRight } from "lucide-react"
import { useLiveMarketInsightsStore } from "@/stores"

interface PriceChange {
  priceName: string
  newPrice: number
}

const sortedKeys = [
  "gold-price-region24", // GOLD 24
  "gold-price-region21", // GOLD 21
  "goldsounces", // GOLD
  "silversounces" // SILVER
]

const initialPrices: Record<string, number | null> = {
  "gold-price-region24": null,
  "gold-price-region21": null,
  goldsounces: null,
  silversounces: null
}

const displayNames: Record<string, string> = {
  "gold-price-region24": "Gold 24K",
  "gold-price-region21": "Gold 21K",
  goldsounces: "Gold",
  silversounces: "Silver"
}

export default function LivePriceWidget() {
  const [prices, setPrices] = useState(initialPrices)
  const prevPricesRef = useRef(initialPrices)

  const { marketData } = useLiveMarketInsightsStore()

  console.log({ marketData })

  useLivePrices((data) => {
    setPrices((prev) => {
      const updated = { ...prev }
      data.priceChanges.forEach(({ priceName, newPrice }: PriceChange) => {
        if (sortedKeys.includes(priceName)) {
          updated[priceName] = newPrice
        }
      })
      prevPricesRef.current = prev
      return updated
    })
  })

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 px-4 gap-4 border-y border-opacity-30 border-secondary-100 mb-6 py-4">
      {sortedKeys.map((key, index) => {
        const value = prices[key]
        const prevValue = prevPricesRef.current[key]
        const hasChanged = prevValue !== null && value !== null
        const isUp = hasChanged ? value! > prevValue! : null

        return (
          <div
            key={key}
            className={cn(
              "text-center",
              index !== sortedKeys.length - 1 &&
                "border-r border-secondary-100/30"
            )}
          >
            <h3 className="text-2xl font-semibold">{displayNames[key]}</h3>
            <p
              className={cn(
                "text-lg font-bold tracking-wide flex items-center justify-center gap-1",
                isUp === true && "text-green-500",
                isUp === false && "text-red-500",
                isUp === null && "text-gray-400"
              )}
            >
              {value !== null ? (
                <>
                  {value.toFixed(2)} SAR
                  {isUp === true && <ArrowUpRight size={18} />}
                  {isUp === false && <ArrowDownRight size={18} />}
                </>
              ) : (
                "Loading..."
              )}
            </p>
          </div>
        )
      })}
    </div>
  )
}
