"use client"

import { LanguageToggle } from "@/components/common/LanguageToggle"
import { ThemeToggle } from "@/components/common/ThemeToggle"
import { SideMenuBar } from "./sidemenubar"
import { useSidebarStore } from "@/stores/sidebarStore"
import { Icon } from "@/components/common/Icon"
import Logo from "@/components/common/logo"
import { useLanguageStore } from "@/stores/languageStore"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { useSystemSettingsWithStore } from "@/hooks"

export function Header({
  isBlogDetailsPage = false
}: {
  isBlogDetailsPage?: boolean
}) {
  const { isRTL } = useLanguageStore()
  const { toggleSidebar } = useSidebarStore()
  const pathname = usePathname()
  const [hash, setHash] = useState("")
  const { getSettingsByKeyPattern, isLoading } = useSystemSettingsWithStore()
  const menu = getSettingsByKeyPattern("HEAD_SECTION_MENU_")
  const [ready, setReady] = useState(false)

  useEffect(() => {
    if (!isLoading) {
      setReady(true)
    }
  }, [isLoading])

  useEffect(() => {
    if (typeof window !== "undefined") {
      setHash(window.location.hash)
      const handleHashChange = () => setHash(window.location.hash)
      window.addEventListener("hashchange", handleHashChange)
      return () => window.removeEventListener("hashchange", handleHashChange)
    }
  }, [])

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/"
    }

    if (href.startsWith("#")) {
      return hash === href
    }

    return pathname.startsWith(href)
  }
  const NAV_ITEM_Right_Side = [
    {
      id: menu[0]?.id,
      key: menu[0]?.key,
      href: "/",
      label: menu[0]?.value
    },
    {
      id: menu[1]?.id,
      key: menu[1]?.key,
      href: "#about",
      label: menu[1]?.value
    }
  ]
  const NAV_ITEM_Left_Side = [
    {
      id: menu[2]?.id,
      key: menu[2]?.key,
      href: "#services",
      label: menu[2]?.value
    },
    {
      id: menu[3]?.id,
      key: menu[3]?.key,
      href: "/market-insights",
      label: menu[3]?.value
    }
  ]
  if (!ready || !menu.length) {
    return null // أو يمكنك إظهار Skeleton
  }
  return (
    <header
      className={cn(
        " w-full bg-transparent min-h-30",
        "absolute top-0 z-40",
        isBlogDetailsPage && "relative border-b min-h-32 pb-4 dark:border-none"
      )}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="grid grid-cols-2 md:grid-cols-12 h-20 md:h-30 gap-4">
          <SideMenuBar />

          {/* Sidebar Button - 1 column */}
          <div className="col-span-2 flex items-center">
            <button
              type="button"
              onClick={toggleSidebar}
              className={cn(
                "p-2 rounded-lg text-primary-50 hover:text-primary-50 hover:bg-button-background-icon transition-colors duration-200",
                isBlogDetailsPage && "text-secondary-500 dark :text-primary-50"
              )}
              aria-label="Open sidebar"
            >
              <Icon
                name={isRTL ? "arrow-menu-alt-right" : "arrow-menu-alt-left"}
                size={24}
              />
            </button>
          </div>

          {/* Navigation with Logo - 8 columns */}
          <nav className="col-span-8 hidden md:grid grid-cols-5 items-center gap-4">
            {/* Right side navigation */}
            {NAV_ITEM_Right_Side.map((item) =>
              item.href.startsWith("#") ? (
                <a
                  key={item.key}
                  href={item.href}
                  className={cn(
                    "text-lg font-medium text-center transition-colors duration-200",
                    isActive(item.href)
                      ? "text-primary-400"
                      : "text-primary-50 hover:text-primary-400",
                    isBlogDetailsPage &&
                      "text-secondary-500 dark:text-primary-50 "
                  )}
                >
                  {item.label}˝
                </a>
              ) : (
                <Link
                  key={item.key}
                  href={item.href}
                  className={cn(
                    "text-lg font-medium text-center transition-colors duration-200",
                    isActive(item.href)
                      ? "text-primary-400"
                      : "text-primary-50 hover:text-primary-400",
                    isBlogDetailsPage &&
                      "text-secondary-500 dark:text-primary-50 "
                  )}
                >
                  {item.label}
                </Link>
              )
            )}

            {/* Logo/Brand - Center */}
            <div className="flex items-center justify-center mt-3">
              <Logo
                width={140}
                height={45}
                priority={true}
                onClick={() => (window.location.href = "/")}
                className="transition-transform duration-200 hover:scale-105 cursor-pointer"
                white
              />
            </div>

            {/* Left side navigation */}
            {NAV_ITEM_Left_Side.map((item) =>
              item.href.startsWith("#") ? (
                <a
                  key={item.key}
                  href={item.href}
                  className={cn(
                    "text-lg font-medium text-center transition-colors duration-200",
                    isActive(item.href)
                      ? "text-primary-400"
                      : "text-primary-50 hover:text-primary-400",
                    isBlogDetailsPage &&
                      "text-secondary-500 dark:text-primary-50 "
                  )}
                >
                  {item.label}
                </a>
              ) : (
                <Link
                  key={item.key}
                  href={item.href}
                  className={cn(
                    "text-lg font-medium text-center transition-colors duration-200",
                    isActive(item.href)
                      ? "text-primary-400"
                      : "text-primary-50 hover:text-primary-400",
                    isBlogDetailsPage &&
                      "text-secondary-500 dark:text-primary-50 "
                  )}
                >
                  {item.label}
                </Link>
              )
            )}
          </nav>

          {/* Right side actions - 3 columns */}
          <div className="col-span-1 md:col-span-2 flex items-center justify-end space-x-4 rtl:space-x-reverse">
            <ThemeToggle isBlogDetailsPage={isBlogDetailsPage} />
            <LanguageToggle isBlogDetailsPage={isBlogDetailsPage} />
          </div>
        </div>
      </div>
    </header>
  )
}
