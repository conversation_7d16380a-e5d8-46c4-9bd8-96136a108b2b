"use client"

import { useTranslation } from "react-i18next"
import { Icon } from "@/components/common/Icon"
import Logo from "@/components/common/logo"
import { useFooterWithStore } from "@/hooks"

export function Footer() {
  const { t } = useTranslation()
  const { data: footer, isLoading, error } = useFooterWithStore()

  const socialLinks = (footer?.social_media_links || []).map((social) => ({
    href: social.url,
    icon: social.type
  }))

  return (
    <footer className="bg-white-50 border-t border-secondary-500 dark:bg-secondary-600 text-white">
      <div className="container mx-auto pt-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Company Info - Left Column (4 columns) */}
          <div className="lg:col-span-5">
            {/* Logo */}
            <div className="mb-6">
              <Logo
                logo={footer?.logo}
                width={140}
                height={45}
                className="mb-6"
              />
            </div>

            {/* Company Description */}
            <p className="text-gray-600 text-lg mb-8 font-normal font-body">
              {footer?.rich_text_section}
            </p>

            {/* Social Media Icons */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.icon}
                  href={social.href}
                  className="text-primary-600 hover:text-primary-400 p-1 bg-blur border border-white-500 dark:border-button-icon-border rounded-sm transition-colors duration-200"
                  aria-label={social.icon}
                >
                  <Icon name={social.icon as any} size={20} />
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links - Middle Column (3 columns) */}
          <div className="lg:col-span-4 lg:ml-8">
            <h3 className="text-primary-500 font-semibold text-xl mb-6">
              {footer?.quick_links.title}
            </h3>
            <div className="grid grid-cols-2">
              <ul className="space-y-4">
                {footer?.quick_links.links.slice(0, 4).map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.url}
                      className="text-black-500 dark:text-gray-600  transition-colors duration-200 text-lg"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
              <ul className="space-y-4">
                {footer?.quick_links.links.slice(4, 6).map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.url}
                      className="text-black-500 dark:text-gray-600  transition-colors duration-200 text-lg"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Legal - Right Column (3 columns) */}
          <div className="lg:col-span-3">
            <h3 className="text-primary-500 font-semibold text-lg mb-6">
              {t("footer.legal")}
            </h3>
            <ul className="space-y-4">
              {footer?.legal_links.links.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.url}
                    className=" text-black-500 dark:text-gray-600  transition-colors duration-200 text-lg"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-16 py-6 border-t border-secondary-500">
          <div className="text-center">
            <p className="text-gray-400 text-md tracking-wider">
              {footer?.copyright_text}
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
