'use client'

import { useEffect } from 'react'
import { useFooter } from '@/hooks/api'
import { useFooterStore } from '@/stores/footerStore'

export const useFooterWithStore = () => {
  const { setFooter } = useFooterStore()
  
  const query = useFooter()
  
  // Update Zustand store when React Query data changes
  useEffect(() => {
    if (query.data) {
      setFooter(query.data)
    }
  }, [query.data, setFooter])
  
  return query
}
